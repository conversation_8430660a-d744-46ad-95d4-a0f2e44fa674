// Simple client-side i18n for static export
export const locales = ['en', 'ja', 'ko', 'es', 'fr', 'de', 'it', 'pt', 'hi', 'vi', 'zh'] as const;
export type Locale = typeof locales[number];

// Import all messages
import en from '@/messages/en.json';
import ja from '@/messages/ja.json';
import ko from '@/messages/ko.json';
import es from '@/messages/es.json';
import fr from '@/messages/fr.json';
import de from '@/messages/de.json';
import it from '@/messages/it.json';
import pt from '@/messages/pt.json';
import hi from '@/messages/hi.json';
import vi from '@/messages/vi.json';
import zh from '@/messages/zh.json';

const messages = {
  en,
  ja,
  ko,
  es,
  fr,
  de,
  it,
  pt,
  hi,
  vi,
  zh
};

export function getMessages(locale: Locale) {
  return messages[locale] || messages.en;
}

export function t(key: string, locale: Locale, messages?: any): string {
  const msgs = messages || getMessages(locale);
  const keys = key.split('.');
  
  let value: any = msgs;
  for (const k of keys) {
    value = value?.[k];
  }
  
  return typeof value === 'string' ? value : key;
}

export const localeNames: Record<Locale, string> = {
  en: 'English',
  ja: '日本語',
  ko: '한국어',
  es: 'Español',
  fr: 'Français',
  de: 'Deutsch',
  it: 'Italiano',
  pt: 'Português',
  hi: 'हिन्दी',
  vi: 'Tiếng Việt',
  zh: '中文'
};
