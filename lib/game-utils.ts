import { Point, GameStats, RatingClass } from '@/types/game';

export function calculateCircleScore(points: Point[]): {
  score: number;
  centerPoint: Point;
  perfectRadius: number;
} {
  if (points.length < 10) {
    return { score: 0, centerPoint: { x: 0, y: 0 }, perfectRadius: 0 };
  }

  // Close the shape if it's not already closed
  const firstPoint = points[0];
  const lastPoint = points[points.length - 1];
  const distance = Math.sqrt((firstPoint.x - lastPoint.x) ** 2 + (firstPoint.y - lastPoint.y) ** 2);

  let processedPoints = [...points];
  if (distance > 20 && points.length > 20) {
    processedPoints.push(firstPoint); // Close the shape
  }

  // Find optimal center using least squares method for better accuracy
  const center = findOptimalCenter(processedPoints);

  // Calculate distances from optimal center
  const distances = processedPoints.map(point =>
    Math.sqrt((point.x - center.x) ** 2 + (point.y - center.y) ** 2)
  );

  // Use median radius instead of average for better robustness
  const sortedDistances = [...distances].sort((a, b) => a - b);
  const medianRadius = sortedDistances[Math.floor(sortedDistances.length / 2)];

  // Calculate multiple scoring factors
  const radiusConsistency = calculateRadiusConsistency(distances, medianRadius);
  const shapeCompleteness = calculateShapeCompleteness(processedPoints, center, medianRadius);
  const smoothness = calculateSmoothness(processedPoints);

  // Combine factors with weights
  const consistencyWeight = 0.6;
  const completenessWeight = 0.25;
  const smoothnessWeight = 0.15;

  const combinedScore =
    radiusConsistency * consistencyWeight +
    shapeCompleteness * completenessWeight +
    smoothness * smoothnessWeight;

  const score = Math.round(Math.max(0, Math.min(100, combinedScore)));

  return {
    score,
    centerPoint: center,
    perfectRadius: medianRadius
  };
}

// Find optimal center using least squares method
function findOptimalCenter(points: Point[]): Point {
  // Start with centroid as initial guess
  let centerX = points.reduce((sum, p) => sum + p.x, 0) / points.length;
  let centerY = points.reduce((sum, p) => sum + p.y, 0) / points.length;

  // Iteratively improve center position (simplified Gauss-Newton)
  for (let iteration = 0; iteration < 5; iteration++) {
    let sumX = 0, sumY = 0, sumWeight = 0;

    points.forEach(point => {
      const dist = Math.sqrt((point.x - centerX) ** 2 + (point.y - centerY) ** 2);
      if (dist > 0) {
        const weight = 1 / (1 + dist * 0.01); // Weight closer points more
        sumX += point.x * weight;
        sumY += point.y * weight;
        sumWeight += weight;
      }
    });

    if (sumWeight > 0) {
      centerX = sumX / sumWeight;
      centerY = sumY / sumWeight;
    }
  }

  return { x: centerX, y: centerY };
}

// Calculate how consistent the radius is
function calculateRadiusConsistency(distances: number[], targetRadius: number): number {
  if (targetRadius === 0) return 0;

  const deviations = distances.map(dist => Math.abs(dist - targetRadius));
  const averageDeviation = deviations.reduce((sum, dev) => sum + dev, 0) / deviations.length;

  // Normalize deviation relative to radius
  const normalizedDeviation = Math.min(averageDeviation / targetRadius, 1);

  // Convert to score (0-100)
  return (1 - normalizedDeviation) * 100;
}

// Calculate how complete the circle shape is
function calculateShapeCompleteness(points: Point[], center: Point, radius: number): number {
  if (points.length < 20) return Math.max(0, (points.length - 10) / 10 * 50); // Penalty for too few points

  // Check angular coverage
  const angles = points.map(point =>
    Math.atan2(point.y - center.y, point.x - center.x)
  );

  // Normalize angles to [0, 2π]
  const normalizedAngles = angles.map(angle => angle < 0 ? angle + 2 * Math.PI : angle);
  normalizedAngles.sort((a, b) => a - b);

  // Check for gaps in angular coverage
  let maxGap = 0;
  for (let i = 0; i < normalizedAngles.length; i++) {
    const nextIndex = (i + 1) % normalizedAngles.length;
    let gap = normalizedAngles[nextIndex] - normalizedAngles[i];
    if (gap < 0) gap += 2 * Math.PI;
    maxGap = Math.max(maxGap, gap);
  }

  // Score based on maximum gap (smaller gaps = better score)
  const gapScore = Math.max(0, 100 - (maxGap / (Math.PI / 3)) * 50); // Penalize gaps > 60 degrees

  return gapScore;
}

// Calculate smoothness of the drawn line
function calculateSmoothness(points: Point[]): number {
  if (points.length < 3) return 50;

  let totalCurvature = 0;
  let validSegments = 0;

  for (let i = 1; i < points.length - 1; i++) {
    const p1 = points[i - 1];
    const p2 = points[i];
    const p3 = points[i + 1];

    // Calculate angle change
    const v1 = { x: p2.x - p1.x, y: p2.y - p1.y };
    const v2 = { x: p3.x - p2.x, y: p3.y - p2.y };

    const len1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y);
    const len2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y);

    if (len1 > 0 && len2 > 0) {
      const dot = (v1.x * v2.x + v1.y * v2.y) / (len1 * len2);
      const angle = Math.acos(Math.max(-1, Math.min(1, dot)));
      totalCurvature += angle;
      validSegments++;
    }
  }

  if (validSegments === 0) return 50;

  const averageCurvature = totalCurvature / validSegments;

  // Ideal curvature for a circle drawn with this many points
  const idealCurvature = (2 * Math.PI) / points.length;
  const curvatureRatio = Math.min(averageCurvature / idealCurvature, 2);

  // Score based on how close to ideal curvature
  return Math.max(0, 100 - Math.abs(curvatureRatio - 1) * 100);
}

export function getRating(score: number): { text: string; class: RatingClass } {
  if (score >= 95) {
    return { text: 'PERFECT! 🎯', class: 'perfect' };
  } else if (score >= 85) {
    return { text: 'EXCELLENT! 🌟', class: 'excellent' };
  } else if (score >= 70) {
    return { text: 'GOOD! 👍', class: 'good' };
  } else if (score >= 50) {
    return { text: 'KEEP TRYING! 💪', class: 'poor' };
  } else if (score >= 30) {
    return { text: 'OOPS! 😅', class: 'poor' };
  } else {
    return { text: 'WHAT IS THAT? 🤔', class: 'poor' };
  }
}

export function updateGameStats(stats: GameStats, newScore: number): GameStats {
  return {
    attempts: stats.attempts + 1,
    totalScore: stats.totalScore + newScore,
    bestScore: Math.max(stats.bestScore, newScore)
  };
}

export function getAverageScore(stats: GameStats): number {
  if (stats.attempts === 0) return 0;
  return Math.round(stats.totalScore / stats.attempts);
}

// Re-export storage functions for backward compatibility
export { saveGameStats, loadGameStats } from './storage';
