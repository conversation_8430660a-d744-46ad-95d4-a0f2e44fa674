'use client';

import { useCallback, useRef, useState, useEffect } from 'react';

interface SoundEffects {
  drawStart: () => void;
  drawing: () => void;
  drawEnd: () => void;
  success: (score: number) => void;
  perfect: () => void;
  enabled: boolean;
  toggleSound: () => void;
}

export function useSoundEffects(): SoundEffects {
  const [enabled, setEnabled] = useState(true);
  const audioContextRef = useRef<AudioContext | null>(null);
  const drawingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Initialize audio context on first user interaction
    const initAudio = () => {
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      }
    };

    document.addEventListener('click', initAudio, { once: true });
    document.addEventListener('touchstart', initAudio, { once: true });

    return () => {
      document.removeEventListener('click', initAudio);
      document.removeEventListener('touchstart', initAudio);
    };
  }, []);

  const createTone = useCallback((frequency: number, duration: number, type: OscillatorType = 'sine', volume: number = 0.1) => {
    if (!enabled || !audioContextRef.current) return;

    try {
      const oscillator = audioContextRef.current.createOscillator();
      const gainNode = audioContextRef.current.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContextRef.current.destination);

      oscillator.frequency.setValueAtTime(frequency, audioContextRef.current.currentTime);
      oscillator.type = type;

      gainNode.gain.setValueAtTime(0, audioContextRef.current.currentTime);
      gainNode.gain.linearRampToValueAtTime(volume, audioContextRef.current.currentTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContextRef.current.currentTime + duration);

      oscillator.start(audioContextRef.current.currentTime);
      oscillator.stop(audioContextRef.current.currentTime + duration);
    } catch (error) {
      console.warn('Audio playback failed:', error);
    }
  }, [enabled]);

  const drawStart = useCallback(() => {
    createTone(440, 0.1, 'sine', 0.05);
  }, [createTone]);

  const drawing = useCallback(() => {
    if (drawingIntervalRef.current) {
      clearInterval(drawingIntervalRef.current);
    }

    drawingIntervalRef.current = setInterval(() => {
      const frequency = 200 + Math.random() * 100;
      createTone(frequency, 0.05, 'triangle', 0.02);
    }, 100);
  }, [createTone]);

  const drawEnd = useCallback(() => {
    if (drawingIntervalRef.current) {
      clearInterval(drawingIntervalRef.current);
      drawingIntervalRef.current = null;
    }
    createTone(330, 0.2, 'sine', 0.08);
  }, [createTone]);

  const success = useCallback((score: number) => {
    if (score >= 95) {
      perfect();
      return;
    }

    const baseFreq = 440;
    const frequency = baseFreq + (score / 100) * 220;
    createTone(frequency, 0.3, 'triangle', 0.1);
    
    setTimeout(() => {
      createTone(frequency * 1.25, 0.2, 'sine', 0.08);
    }, 150);
  }, [createTone]);

  const perfect = useCallback(() => {
    // Play a celebratory sequence
    const notes = [523, 659, 784, 1047]; // C, E, G, C (major chord)
    notes.forEach((freq, index) => {
      setTimeout(() => {
        createTone(freq, 0.4, 'triangle', 0.12);
      }, index * 100);
    });

    // Add some sparkle
    setTimeout(() => {
      for (let i = 0; i < 5; i++) {
        setTimeout(() => {
          createTone(1047 + Math.random() * 500, 0.1, 'sine', 0.06);
        }, i * 50);
      }
    }, 400);
  }, [createTone]);

  const toggleSound = useCallback(() => {
    setEnabled(prev => !prev);
  }, []);

  return {
    drawStart,
    drawing,
    drawEnd,
    success,
    perfect,
    enabled,
    toggleSound,
  };
}
