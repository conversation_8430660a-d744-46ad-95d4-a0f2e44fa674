'use client';

import { useCallback } from 'react';

interface HapticFeedback {
  light: () => void;
  medium: () => void;
  heavy: () => void;
  success: () => void;
  error: () => void;
}

export function useHapticFeedback(): HapticFeedback {
  const vibrate = useCallback((pattern: number | number[]) => {
    if ('vibrate' in navigator) {
      try {
        navigator.vibrate(pattern);
      } catch (error) {
        console.warn('Vibration not supported:', error);
      }
    }
  }, []);

  const light = useCallback(() => {
    vibrate(10);
  }, [vibrate]);

  const medium = useCallback(() => {
    vibrate(25);
  }, [vibrate]);

  const heavy = useCallback(() => {
    vibrate(50);
  }, [vibrate]);

  const success = useCallback(() => {
    vibrate([50, 50, 100]);
  }, [vibrate]);

  const error = useCallback(() => {
    vibrate([100, 50, 100]);
  }, [vibrate]);

  return {
    light,
    medium,
    heavy,
    success,
    error,
  };
}
