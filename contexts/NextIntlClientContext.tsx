'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Locale, getMessages, t as translate, locales } from '@/lib/next-intl-client';

interface NextIntlContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: (key: string) => string;
  messages: any;
}

const NextIntlContext = createContext<NextIntlContextType | undefined>(undefined);

export function NextIntlClientProvider({ 
  children, 
  initialLocale 
}: { 
  children: ReactNode;
  initialLocale: Locale;
}) {
  const [locale, setLocaleState] = useState<Locale>(initialLocale);
  const [messages, setMessages] = useState(getMessages(initialLocale));
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Update messages when locale changes
    setMessages(getMessages(locale));
  }, [locale]);

  const setLocale = (newLocale: Locale) => {
    setLocaleState(newLocale);
    // Navigate to new locale
    router.push(`/${newLocale}/`);
  };

  const t = (key: string) => translate(key, locale, messages);

  return (
    <NextIntlContext.Provider value={{ locale, setLocale, t, messages }}>
      {children}
    </NextIntlContext.Provider>
  );
}

export function useTranslations() {
  const context = useContext(NextIntlContext);
  if (context === undefined) {
    throw new Error('useTranslations must be used within a NextIntlClientProvider');
  }
  return context.t;
}

export function useLocale() {
  const context = useContext(NextIntlContext);
  if (context === undefined) {
    throw new Error('useLocale must be used within a NextIntlClientProvider');
  }
  return context.locale;
}
