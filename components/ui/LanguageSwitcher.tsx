'use client';

import React from 'react';
import { useI18n } from '@/contexts/I18nContext';
import { supportedLocales, Locale } from '@/lib/i18n';
import { Globe } from 'lucide-react';

export function LanguageSwitcher() {
  const { locale, setLocale } = useI18n();

  const handleLanguageChange = (newLocale: Locale) => {
    setLocale(newLocale);
  };

  return (
    <div className="relative group">
      <button className="flex items-center gap-2 px-3 py-2 text-gray-300 hover:text-white transition-colors rounded-lg hover:bg-white/10 backdrop-blur-sm">
        <Globe className="w-4 h-4" />
        <span className="text-sm font-medium">
          {supportedLocales.find(lang => lang.code === locale)?.name || 'EN'}
        </span>
      </button>

      <div className="absolute right-0 top-full mt-1 bg-white/95 backdrop-blur-md border border-gray-200 rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
        <div className="py-1 min-w-[120px]">
          {supportedLocales.map((lang) => (
            <button
              key={lang.code}
              onClick={() => handleLanguageChange(lang.code)}
              className={`w-full text-left px-4 py-2 text-sm hover:bg-blue-50 transition-colors ${
                locale === lang.code ? 'text-blue-600 font-medium bg-blue-50' : 'text-gray-700'
              }`}
            >
              {lang.name}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
