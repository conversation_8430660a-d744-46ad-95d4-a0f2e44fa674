'use client';

import React, { useContext } from 'react';
import { useLocale } from '@/contexts/NextIntlClientContext';
import { useRouter, usePathname } from 'next/navigation';
import { Globe } from 'lucide-react';
import { locales, localeNames, Locale } from '@/lib/next-intl-client';

export function LocaleSwitcher() {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const handleLanguageChange = (newLocale: Locale) => {
    // For static export, navigate to the new locale path
    router.push(`/${newLocale}/`);
  };

  return (
    <div className="relative group">
      <button className="flex items-center gap-2 px-3 py-2 text-gray-300 hover:text-white transition-colors rounded-lg hover:bg-white/10 backdrop-blur-sm">
        <Globe className="w-4 h-4" />
        <span className="text-sm font-medium">
          {localeNames[locale] || 'EN'}
        </span>
      </button>
      
      <div className="absolute right-0 top-full mt-1 bg-white/95 backdrop-blur-md border border-gray-200 rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
        <div className="py-1 min-w-[120px]">
          {locales.map((loc) => (
            <button
              key={loc}
              onClick={() => handleLanguageChange(loc)}
              className={`w-full text-left px-4 py-2 text-sm hover:bg-blue-50 transition-colors ${
                locale === loc ? 'text-blue-600 font-medium bg-blue-50' : 'text-gray-700'
              }`}
            >
              {localeNames[loc]}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
