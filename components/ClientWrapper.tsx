'use client';

import React from 'react';
import { CircleGame } from "@/components/game/CircleGame"
import { Header } from "@/components/layout/Header"
import { FeaturesSection } from "@/components/seo/FeaturesSection"
import { HowItWorksSection } from "@/components/seo/HowItWorksSection"
import { FAQSection } from "@/components/seo/FAQSection"
import { BenefitsSection } from "@/components/seo/BenefitsSection"

export function ClientWrapper() {
  return (
    <>
      {/* Main game section */}
      <main className="min-h-screen bg-gray-900 flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8 relative overflow-hidden">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"></div>
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl"></div>
        </div>

        <div className="w-full max-w-4xl mx-auto relative z-10">
          <Header />
          <CircleGame />
        </div>
      </main>

      {/* SEO Content Sections */}
      <FeaturesSection />
      <HowItWorksSection />
      <BenefitsSection />
      <FAQSection />
    </>
  )
}
