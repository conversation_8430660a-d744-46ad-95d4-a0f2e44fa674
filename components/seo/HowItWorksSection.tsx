'use client';

import React from 'react';
import { useTranslations } from '@/contexts/NextIntlClientContext';

export function HowItWorksSection() {
  const t = useTranslations();

  const steps = [
    {
      key: 'step1',
      number: '1',
      icon: '✏️',
    },
    {
      key: 'step2',
      number: '2',
      icon: '📊',
    },
    {
      key: 'step3',
      number: '3',
      icon: '🏆',
    },
  ];

  return (
    <section className="py-16 bg-gray-900/30" id="how-it-works">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            {t('howItWorks.title')}
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            {t('howItWorks.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {steps.map((step, index) => (
            <div
              key={step.key}
              className="text-center relative"
            >
              {/* Connection line */}
              {index < steps.length - 1 && (
                <div className="hidden md:block absolute top-16 left-1/2 w-full h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 transform translate-x-1/2 z-0" />
              )}
              
              <div className="relative z-10 bg-gradient-to-br from-gray-800/80 to-gray-900/90 backdrop-blur-sm rounded-full w-32 h-32 mx-auto mb-6 flex items-center justify-center border-4 border-blue-500/60 shadow-lg hover:border-blue-400/80 transition-all duration-300 hover:scale-110">
                <div className="text-center">
                  <div className="text-3xl mb-1 filter drop-shadow-lg">{step.icon}</div>
                  <div className="text-blue-300 font-bold text-lg">{step.number}</div>
                </div>
              </div>
              
              <h3 className="text-xl font-semibold text-white mb-3">
                {t(`howItWorks.${step.key}.title`)}
              </h3>
              <p className="text-gray-300 leading-relaxed">
                {t(`howItWorks.${step.key}.description`)}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
