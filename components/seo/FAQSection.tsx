'use client';

import React, { useState } from 'react';
import { useTranslations } from '@/contexts/NextIntlClientContext';
import { cn } from '@/lib/utils';

export function FAQSection() {
  const t = useTranslations();
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = ['q1', 'q2', 'q3', 'q4', 'q5'];

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-16 bg-gray-800/30" id="faq">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            {t('faq.title')}
          </h2>
        </div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={faq}
              className="bg-gradient-to-br from-gray-800/60 to-gray-900/80 backdrop-blur-sm border border-gray-600/20 rounded-xl overflow-hidden shadow-lg hover:border-gray-500/30 transition-all duration-300"
            >
              <button
                className="w-full px-6 py-4 text-left flex justify-between items-center hover:from-gray-700/70 hover:to-gray-800/90 transition-all duration-200"
                onClick={() => toggleFAQ(index)}
              >
                <h3 className="text-lg font-semibold text-white pr-4">
                  {t(`faq.${faq}.question`)}
                </h3>
                <div className={cn(
                  "text-blue-300 text-xl transition-transform duration-200 filter drop-shadow-sm",
                  openIndex === index ? "rotate-180" : ""
                )}>
                  ▼
                </div>
              </button>
              
              <div className={cn(
                "overflow-hidden transition-all duration-300 ease-in-out",
                openIndex === index ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
              )}>
                <div className="px-6 pb-4">
                  <p className="text-gray-300 leading-relaxed">
                    {t(`faq.${faq}.answer`)}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
