'use client';

import React from 'react';
import { useTranslations } from '@/contexts/NextIntlClientContext';

export function FeaturesSection() {
  const t = useTranslations();

  const features = [
    {
      key: 'realTimeScoring',
      icon: '⚡',
    },
    {
      key: 'multiLanguage',
      icon: '🌍',
    },
    {
      key: 'responsive',
      icon: '📱',
    },
    {
      key: 'noInstall',
      icon: '🚀',
    },
  ];

  return (
    <section className="py-16 bg-gray-800/50" id="features">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            {t('features.title')}
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            {t('features.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature) => (
            <div
              key={feature.key}
              className="bg-gradient-to-br from-gray-800/60 to-gray-900/80 backdrop-blur-sm border border-gray-600/20 rounded-xl p-6 text-center hover:from-gray-700/70 hover:to-gray-800/90 hover:border-gray-500/30 transition-all duration-300 hover:scale-105 shadow-lg"
            >
              <div className="text-4xl mb-4 filter drop-shadow-lg">{feature.icon}</div>
              <h3 className="text-xl font-semibold text-white mb-3">
                {t(`features.${feature.key}.title`)}
              </h3>
              <p className="text-gray-300 leading-relaxed">
                {t(`features.${feature.key}.description`)}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
