'use client';

import React from 'react';
import { useTranslations } from '@/contexts/NextIntlClientContext';

export function BenefitsSection() {
  const t = useTranslations();

  const benefits = [
    {
      key: 'coordination',
      icon: '🎯',
      gradient: 'from-blue-500 to-cyan-500',
    },
    {
      key: 'focus',
      icon: '🧠',
      gradient: 'from-purple-500 to-pink-500',
    },
    {
      key: 'stress',
      icon: '🧘',
      gradient: 'from-green-500 to-emerald-500',
    },
    {
      key: 'fun',
      icon: '🎮',
      gradient: 'from-orange-500 to-red-500',
    },
  ];

  return (
    <section className="py-16 bg-gray-900/50" id="benefits">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            {t('benefits.title')}
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            {t('benefits.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {benefits.map((benefit) => (
            <div
              key={benefit.key}
              className="group relative bg-gradient-to-br from-gray-800/60 to-gray-900/80 backdrop-blur-sm border border-gray-600/20 rounded-xl p-8 hover:from-gray-700/70 hover:to-gray-800/90 hover:border-gray-500/30 transition-all duration-300 hover:scale-105 shadow-lg"
            >
              <div className={`absolute inset-0 bg-gradient-to-r ${benefit.gradient} opacity-0 group-hover:opacity-15 rounded-xl transition-opacity duration-300`} />

              <div className="relative z-10">
                <div className="text-5xl mb-4 filter drop-shadow-lg">{benefit.icon}</div>
                <h3 className="text-xl font-semibold text-white mb-3">
                  {t(`benefits.${benefit.key}.title`)}
                </h3>
                <p className="text-gray-300 leading-relaxed">
                  {t(`benefits.${benefit.key}.description`)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
