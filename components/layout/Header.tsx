'use client';

import React from 'react';
import { LocaleSwitcher } from '@/components/ui/LocaleSwitcher';
import { useTranslations } from '@/contexts/NextIntlClientContext';

export function Header() {
  const t = useTranslations();

  return (
    <div className="relative text-center mb-6 sm:mb-8">
      <div className="absolute top-0 right-0">
        <LocaleSwitcher />
      </div>
      <h1 className="text-3xl sm:text-4xl lg:text-5xl font-light text-white mb-2 sm:mb-3 tracking-tight px-4 perfect-circle-title">
        {t('title')}
      </h1>
      <h2 className="text-gray-300 text-lg sm:text-xl font-light px-4">
        {t('subtitle')}
      </h2>
      <div className="mt-3 sm:mt-4 w-16 sm:w-24 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 mx-auto rounded-full shadow-lg" />
    </div>
  );
}
