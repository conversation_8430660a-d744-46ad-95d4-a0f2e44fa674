'use client';

import React, { useRef, useEffect, useCallback, useState } from 'react';
import { Point } from '@/types/game';
import { useSoundEffects } from '@/hooks/useSoundEffects';
import { useHapticFeedback } from '@/hooks/useHapticFeedback';

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  maxLife: number;
  color: string;
}

interface EnhancedCanvasProps {
  points: Point[];
  isDrawing: boolean;
  showScore: boolean;
  centerPoint: Point | null;
  perfectRadius: number | null;
  onStartDrawing: (point: Point) => void;
  onDraw: (point: Point) => void;
  onStopDrawing: () => void;
}

export function EnhancedCanvas({
  points,
  isDrawing,
  showScore,
  centerPoint,
  perfectRadius,
  onStartDrawing,
  onDraw,
  onStopDrawing
}: EnhancedCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const animationFrameRef = useRef<number>();
  const lastDrawTimeRef = useRef<number>(0);
  const [canvasSize, setCanvasSize] = useState({ width: 400, height: 400 });
  
  const soundEffects = useSoundEffects();
  const hapticFeedback = useHapticFeedback();

  // Update canvas size based on container and device
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        let maxSize = 400;
        if (viewportWidth < 640) {
          maxSize = Math.min(viewportWidth - 32, viewportHeight * 0.4, 350);
        } else if (viewportWidth < 1024) {
          maxSize = Math.min(rect.width, rect.height, 380);
        }

        const size = Math.min(rect.width, rect.height, maxSize);
        setCanvasSize({ width: size, height: size });
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    window.addEventListener('orientationchange', updateSize);
    return () => {
      window.removeEventListener('resize', updateSize);
      window.removeEventListener('orientationchange', updateSize);
    };
  }, []);

  const getEventPos = useCallback((e: MouseEvent | TouchEvent): Point => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    
    const clientX = 'clientX' in e ? e.clientX : e.touches[0].clientX;
    const clientY = 'clientY' in e ? e.clientY : e.touches[0].clientY;
    
    return {
      x: (clientX - rect.left) * scaleX,
      y: (clientY - rect.top) * scaleY
    };
  }, []);

  const addParticle = useCallback((x: number, y: number, color: string) => {
    const particle: Particle = {
      x,
      y,
      vx: (Math.random() - 0.5) * 4,
      vy: (Math.random() - 0.5) * 4,
      life: 30,
      maxLife: 30,
      color
    };
    particlesRef.current.push(particle);
  }, []);

  const updateParticles = useCallback(() => {
    particlesRef.current = particlesRef.current.filter(particle => {
      particle.x += particle.vx;
      particle.y += particle.vy;
      particle.life--;
      particle.vx *= 0.98;
      particle.vy *= 0.98;
      return particle.life > 0;
    });
  }, []);

  const getLineColor = useCallback((progress: number, speed: number) => {
    const hue = (progress * 360 + speed * 50) % 360;
    const saturation = 70 + speed * 30;
    const lightness = 50 + speed * 20;
    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  }, []);

  const handleMouseDown = useCallback((e: MouseEvent) => {
    e.preventDefault();
    const pos = getEventPos(e);
    onStartDrawing(pos);
    soundEffects.drawStart();
    hapticFeedback.light();
    soundEffects.drawing();
  }, [getEventPos, onStartDrawing, soundEffects, hapticFeedback]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDrawing) return;
    e.preventDefault();
    const pos = getEventPos(e);
    onDraw(pos);
    
    const now = Date.now();
    const speed = Math.min((now - lastDrawTimeRef.current) / 10, 10);
    lastDrawTimeRef.current = now;
    
    const progress = points.length / 100;
    const color = getLineColor(progress, speed);
    addParticle(pos.x, pos.y, color);
    
    if (Math.random() < 0.3) {
      hapticFeedback.light();
    }
  }, [isDrawing, getEventPos, onDraw, points.length, getLineColor, addParticle, hapticFeedback]);

  const handleMouseUp = useCallback((e: MouseEvent) => {
    if (!isDrawing) return;
    e.preventDefault();
    onStopDrawing();
    soundEffects.drawEnd();
    hapticFeedback.medium();
  }, [isDrawing, onStopDrawing, soundEffects, hapticFeedback]);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.touches.length === 1) {
      const pos = getEventPos(e);
      onStartDrawing(pos);
      soundEffects.drawStart();
      hapticFeedback.light();
      soundEffects.drawing();
    }
  }, [getEventPos, onStartDrawing, soundEffects, hapticFeedback]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!isDrawing) return;
    e.preventDefault();
    e.stopPropagation();
    if (e.touches.length === 1) {
      const pos = getEventPos(e);
      onDraw(pos);
      
      const now = Date.now();
      const speed = Math.min((now - lastDrawTimeRef.current) / 10, 10);
      lastDrawTimeRef.current = now;
      
      const progress = points.length / 100;
      const color = getLineColor(progress, speed);
      addParticle(pos.x, pos.y, color);
      
      if (Math.random() < 0.2) {
        hapticFeedback.light();
      }
    }
  }, [isDrawing, getEventPos, onDraw, points.length, getLineColor, addParticle, hapticFeedback]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!isDrawing) return;
    e.preventDefault();
    e.stopPropagation();
    onStopDrawing();
    soundEffects.drawEnd();
    hapticFeedback.medium();
  }, [isDrawing, onStopDrawing, soundEffects, hapticFeedback]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('mouseleave', handleMouseUp);
    canvas.addEventListener('touchstart', handleTouchStart, { passive: false });
    canvas.addEventListener('touchmove', handleTouchMove, { passive: false });
    canvas.addEventListener('touchend', handleTouchEnd, { passive: false });

    return () => {
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseup', handleMouseUp);
      canvas.removeEventListener('mouseleave', handleMouseUp);
      canvas.removeEventListener('touchstart', handleTouchStart);
      canvas.removeEventListener('touchmove', handleTouchMove);
      canvas.removeEventListener('touchend', handleTouchEnd);
    };
  }, [handleMouseDown, handleMouseMove, handleMouseUp, handleTouchStart, handleTouchMove, handleTouchEnd]);

  // Animation loop for particles
  useEffect(() => {
    const animate = () => {
      updateParticles();
      animationFrameRef.current = requestAnimationFrame(animate);
    };
    animate();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [updateParticles]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw particles
    particlesRef.current.forEach(particle => {
      const alpha = particle.life / particle.maxLife;
      ctx.save();
      ctx.globalAlpha = alpha;
      ctx.fillStyle = particle.color;
      ctx.beginPath();
      ctx.arc(particle.x, particle.y, 2, 0, 2 * Math.PI);
      ctx.fill();
      ctx.restore();
    });

    // Draw current path with dynamic colors
    if (points.length > 1) {
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      
      for (let i = 1; i < points.length; i++) {
        const progress = i / points.length;
        const speed = 5; // Default speed for static rendering
        const color = getLineColor(progress, speed);
        
        ctx.strokeStyle = color;
        ctx.lineWidth = isDrawing ? 4 : 3;
        ctx.shadowColor = color;
        ctx.shadowBlur = isDrawing ? 8 : 4;
        
        ctx.beginPath();
        ctx.moveTo(points[i - 1].x, points[i - 1].y);
        ctx.lineTo(points[i].x, points[i].y);
        ctx.stroke();
      }
      
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;

      // Draw start point indicator
      if (points.length > 0) {
        ctx.fillStyle = '#10b981';
        ctx.beginPath();
        ctx.arc(points[0].x, points[0].y, 6, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.fillStyle = '#1e293b';
        ctx.beginPath();
        ctx.arc(points[0].x, points[0].y, 3, 0, 2 * Math.PI);
        ctx.fill();
      }

      // Draw current point indicator when drawing
      if (isDrawing && points.length > 0) {
        const lastPoint = points[points.length - 1];
        ctx.fillStyle = '#fbbf24';
        ctx.shadowColor = '#fbbf24';
        ctx.shadowBlur = 10;
        ctx.beginPath();
        ctx.arc(lastPoint.x, lastPoint.y, 5, 0, 2 * Math.PI);
        ctx.fill();
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
      }
    }

    // Draw perfect circle comparison when showing score
    if (showScore && centerPoint && perfectRadius) {
      ctx.strokeStyle = '#94a3b8';
      ctx.lineWidth = 2;
      ctx.setLineDash([10, 5]);
      ctx.globalAlpha = 0.8;
      
      ctx.beginPath();
      ctx.arc(centerPoint.x, centerPoint.y, perfectRadius, 0, 2 * Math.PI);
      ctx.stroke();
      
      ctx.setLineDash([]);
      ctx.globalAlpha = 1;
      
      ctx.fillStyle = '#fbbf24';
      ctx.shadowColor = '#fbbf24';
      ctx.shadowBlur = 8;
      ctx.beginPath();
      ctx.arc(centerPoint.x, centerPoint.y, 4, 0, 2 * Math.PI);
      ctx.fill();
      
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
    }
  }, [points, showScore, centerPoint, perfectRadius, canvasSize, isDrawing, getLineColor]);

  return (
    <div 
      ref={containerRef}
      className="w-full h-full flex items-center justify-center"
    >
      <canvas
        ref={canvasRef}
        width={canvasSize.width}
        height={canvasSize.height}
        className="cursor-crosshair block touch-none"
        style={{
          width: `${canvasSize.width}px`,
          height: `${canvasSize.height}px`
        }}
      />
    </div>
  );
}
