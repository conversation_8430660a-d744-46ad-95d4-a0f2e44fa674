'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface WelcomeAnimationProps {
  onComplete: () => void;
}

export function WelcomeAnimation({ onComplete }: WelcomeAnimationProps) {
  const [stage, setStage] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timers = [
      setTimeout(() => setStage(1), 500),
      setTimeout(() => setStage(2), 1500),
      setTimeout(() => setStage(3), 2500),
      setTimeout(() => {
        setIsVisible(false);
        setTimeout(onComplete, 500);
      }, 3500)
    ];

    return () => timers.forEach(clearTimeout);
  }, [onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-gray-900 z-50 flex items-center justify-center">
      <div className="text-center">
        {/* Title Animation */}
        <div className={cn(
          "transition-all duration-1000 ease-out",
          stage >= 1 ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
        )}>
          <h1 className="perfect-circle-title text-4xl sm:text-6xl mb-4">
            Can you draw a
          </h1>
        </div>

        {/* Perfect Circle Animation */}
        <div className={cn(
          "transition-all duration-1000 ease-out delay-500",
          stage >= 2 ? "opacity-100 translate-y-0 scale-100" : "opacity-0 translate-y-8 scale-95"
        )}>
          <h2 className="text-5xl sm:text-7xl font-bold text-yellow-400 mb-8 tracking-tight">
            PERFECT CIRCLE
          </h2>
        </div>

        {/* Animated Circle */}
        <div className={cn(
          "relative mx-auto transition-all duration-1000 ease-out",
          stage >= 3 ? "opacity-100 scale-100" : "opacity-0 scale-50"
        )}>
          <div className="w-32 h-32 sm:w-40 sm:h-40 mx-auto relative">
            <svg
              className="w-full h-full animate-spin"
              style={{ animationDuration: '3s' }}
              viewBox="0 0 100 100"
            >
              <circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="url(#gradient)"
                strokeWidth="2"
                strokeLinecap="round"
                strokeDasharray="283"
                strokeDashoffset="283"
                className="animate-draw-circle"
              />
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#3b82f6" />
                  <stop offset="50%" stopColor="#8b5cf6" />
                  <stop offset="100%" stopColor="#ef4444" />
                </linearGradient>
              </defs>
            </svg>
          </div>
        </div>

        {/* Question Mark */}
        <div className={cn(
          "text-6xl sm:text-8xl text-white mt-4 transition-all duration-500 ease-out",
          stage >= 3 ? "opacity-100 animate-bounce" : "opacity-0"
        )}>
          ?
        </div>
      </div>
    </div>
  );
}
