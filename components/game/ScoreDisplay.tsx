'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { RatingClass } from '@/types/game';

interface ScoreDisplayProps {
  score: number;
  rating: string;
  ratingClass: RatingClass;
  show: boolean;
  showPerfectCelebration: boolean;
}

export function ScoreDisplay({ 
  score, 
  rating, 
  ratingClass, 
  show, 
  showPerfectCelebration 
}: ScoreDisplayProps) {
  const [animatedScore, setAnimatedScore] = useState(0);

  useEffect(() => {
    if (!show) {
      setAnimatedScore(0);
      return;
    }

    let currentScore = 0;
    const increment = Math.ceil(score / 30);
    
    const countUp = setInterval(() => {
      currentScore += increment;
      if (currentScore >= score) {
        currentScore = score;
        clearInterval(countUp);
      }
      setAnimatedScore(currentScore);
    }, 50);

    return () => clearInterval(countUp);
  }, [score, show]);

  const getRatingStyles = (ratingClass: RatingClass) => {
    switch (ratingClass) {
      case 'perfect':
        return 'bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg animate-pulse';
      case 'excellent':
        return 'bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg';
      case 'good':
        return 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white shadow-lg';
      case 'poor':
        return 'bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  return (
    <>
      <div className={cn(
        "absolute top-3 left-3 right-3 sm:top-5 sm:left-5 sm:right-5 flex justify-between items-center transition-all duration-300 pointer-events-none",
        show ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-2"
      )}>
        <div className="px-3 py-2">
          <span className="text-3xl sm:text-5xl font-semibold text-white drop-shadow-lg">
            {animatedScore}
          </span>
          <span className="text-lg sm:text-2xl text-white/80 ml-1 drop-shadow-lg">%</span>
        </div>
        <div className={cn(
          "text-sm sm:text-lg font-medium px-2 py-1 sm:px-3 sm:py-1.5 rounded-full uppercase tracking-wide",
          getRatingStyles(ratingClass)
        )}>
          {rating}
        </div>
      </div>

      {showPerfectCelebration && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-2xl sm:text-3xl font-bold text-transparent bg-gradient-to-r from-yellow-400 via-red-500 to-pink-500 bg-clip-text pointer-events-none animate-perfect-pulse">
          PERFECT CIRCLE! 🎉✨🎯
        </div>
      )}
    </>
  );
}
