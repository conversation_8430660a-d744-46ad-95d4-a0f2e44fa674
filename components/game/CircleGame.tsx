'use client';

import React, { useEffect } from 'react';
import { EnhancedCanvas } from './EnhancedCanvas';
import { ScoreDisplay } from './ScoreDisplay';
import { StatsDisplay } from './StatsDisplay';
import { TipsDialog } from './TipsDialog';
import { SoundToggle } from '@/components/ui/SoundToggle';
import { useGameState } from '@/hooks/useGameState';
import { useSoundEffects } from '@/hooks/useSoundEffects';
import { useHapticFeedback } from '@/hooks/useHapticFeedback';
import { useTranslations } from '@/contexts/NextIntlClientContext';
import { getRating } from '@/lib/game-utils';

export function CircleGame() {
  const { gameState, gameStats, showHint, actions } = useGameState();
  const soundEffects = useSoundEffects();
  const hapticFeedback = useHapticFeedback();
  const t = useTranslations();

  const ratingInfo = gameState.rating ? getRating(gameState.currentScore || 0) : null;

  // Play success sound when score is shown
  useEffect(() => {
    if (gameState.showScore && gameState.currentScore !== null) {
      if (gameState.currentScore >= 95) {
        soundEffects.perfect();
        hapticFeedback.success();
      } else {
        soundEffects.success(gameState.currentScore);
        if (gameState.currentScore >= 85) {
          hapticFeedback.success();
        } else if (gameState.currentScore >= 70) {
          hapticFeedback.medium();
        } else {
          hapticFeedback.error();
        }
      }
    }
  }, [gameState.showScore, gameState.currentScore, soundEffects, hapticFeedback]);

  return (
    <>
      <SoundToggle />
      <div className="flex flex-col items-center space-y-6 sm:space-y-8 lg:space-y-10 w-full max-w-lg mx-auto px-4 perfect-circle-fade-in">
        <div className="relative w-full aspect-square max-w-[400px] sm:max-w-[450px] perfect-circle-scale-in perfect-circle-game-container">
          <div className="perfect-circle-canvas perfect-circle-glow w-full h-full relative overflow-hidden">
            <EnhancedCanvas
              points={gameState.points}
              isDrawing={gameState.isDrawing}
              showScore={gameState.showScore}
              centerPoint={gameState.centerPoint}
              perfectRadius={gameState.perfectRadius}
              onStartDrawing={actions.startDrawing}
              onDraw={actions.addPoint}
              onStopDrawing={actions.stopDrawing}
            />

            {showHint && !gameState.isDrawing && gameState.points.length === 0 && (
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-slate-300 text-lg text-center pointer-events-none transition-opacity duration-300 animate-pulse perfect-circle-text">
                {t('game.hint')}
              </div>
            )}

            {gameState.showScore && gameState.currentScore !== null && ratingInfo && (
              <ScoreDisplay
                score={gameState.currentScore}
                rating={ratingInfo.text}
                ratingClass={ratingInfo.class}
                show={gameState.showScore}
                showPerfectCelebration={gameState.showPerfectCelebration}
              />
            )}
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 w-full sm:w-auto">
          <button
            onClick={actions.resetGame}
            className="perfect-circle-button w-full sm:w-auto min-h-[48px] text-base"
          >
            {t('game.tryAgain')}
          </button>
          <TipsDialog />
        </div>

        <StatsDisplay stats={gameStats} show={gameStats.attempts > 0} />
      </div>
    </>
  );
}
