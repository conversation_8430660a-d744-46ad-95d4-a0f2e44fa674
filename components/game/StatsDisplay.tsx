'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { GameStats } from '@/types/game';
import { getAverageScore } from '@/lib/game-utils';

interface StatsDisplayProps {
  stats: GameStats;
  show: boolean;
}

export function StatsDisplay({ stats, show }: StatsDisplayProps) {
  return (
    <div className={cn(
      "text-center transition-opacity duration-300",
      show ? "opacity-100" : "opacity-0"
    )}>
      <div className="flex justify-center gap-4 sm:gap-8 text-sm">
        <div className="flex flex-col items-center bg-gradient-to-br from-blue-500/20 to-blue-600/30 backdrop-blur-sm border border-blue-400/20 rounded-lg p-3 min-w-[80px] shadow-lg">
          <span className="text-xs uppercase tracking-wide text-blue-300 font-medium">🏆 Best</span>
          <span className="font-bold text-blue-100 text-lg">
            {stats.bestScore}%
          </span>
        </div>
        <div className="flex flex-col items-center bg-gradient-to-br from-purple-500/20 to-purple-600/30 backdrop-blur-sm border border-purple-400/20 rounded-lg p-3 min-w-[80px] shadow-lg">
          <span className="text-xs uppercase tracking-wide text-purple-300 font-medium">🎯 Tries</span>
          <span className="font-bold text-purple-100 text-lg">
            {stats.attempts}
          </span>
        </div>
        <div className="flex flex-col items-center bg-gradient-to-br from-green-500/20 to-green-600/30 backdrop-blur-sm border border-green-400/20 rounded-lg p-3 min-w-[80px] shadow-lg">
          <span className="text-xs uppercase tracking-wide text-green-300 font-medium">📊 Avg</span>
          <span className="font-bold text-green-100 text-lg">
            {getAverageScore(stats)}%
          </span>
        </div>
      </div>
    </div>
  );
}
