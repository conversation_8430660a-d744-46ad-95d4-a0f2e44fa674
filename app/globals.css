@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Perfect Circle Game Styles */
@layer components {
  /* Animation classes */
  .perfect-circle-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .perfect-circle-scale-in {
    animation: scaleIn 0.5s ease-out;
  }

  /* Canvas styling */
  .perfect-circle-canvas {
    @apply rounded-2xl shadow-2xl border;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%);
    border-color: rgba(148, 163, 184, 0.2);
    backdrop-filter: blur(10px);
  }

  .perfect-circle-glow {
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.4),
      0 10px 10px -5px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(148, 163, 184, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  /* Button styling */
  .perfect-circle-button {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700
           text-white font-medium px-6 py-3 rounded-xl transition-all duration-200
           shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95
           border border-blue-400/20 backdrop-blur-sm;
  }

  /* Game container styling */
  .perfect-circle-game-container {
    @apply relative;
  }

  .perfect-circle-game-container::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    border-radius: 2rem;
    z-index: -1;
    opacity: 0.5;
    animation: subtleGlow 4s ease-in-out infinite alternate;
  }

  /* Subtle glow animation */
  @keyframes subtleGlow {
    0% {
      opacity: 0.3;
      transform: scale(0.98);
    }
    100% {
      opacity: 0.6;
      transform: scale(1.02);
    }
  }

  /* Text styling */
  .perfect-circle-text {
    @apply text-gray-300;
  }

  .perfect-circle-title {
    @apply text-white font-light tracking-tight;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
}

@layer utilities {
  /* Keyframe animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes perfectPulse {
    0%, 100% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.8);
    }
    50% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.2);
    }
  }

  /* Perfect celebration animation */
  .perfect-celebration {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    font-weight: 600;
    color: #4CAF50;
    opacity: 0;
    pointer-events: none;
    animation: perfectPulse 2s ease-in-out;
  }
}
