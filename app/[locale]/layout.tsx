import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import '../globals.css'
import { GoogleAnalytics } from '@/components/analytics/GoogleAnalytics'
import { NextIntlClientProvider } from '@/contexts/NextIntlClientContext'
import { notFound } from 'next/navigation'
import { locales, Locale } from '@/lib/next-intl-client'

const inter = Inter({ subsets: ['latin'] })

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
}

export const metadata: Metadata = {
  title: 'Draw a Perfect Circle - Test Your Circle Drawing Skills Online',
  description: 'Challenge yourself to draw a perfect circle! Test your precision and get scored on how close you can get to drawing the perfect circle. Free online circle drawing game.',
  authors: [{ name: 'Draw Perfect Circle' }],
  openGraph: {
    title: 'Draw a Perfect Circle - Test Your Circle Drawing Skills',
    description: 'Challenge yourself to draw a perfect circle! Test your precision and get scored on how close you can get to drawing the perfect circle.',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Draw a Perfect Circle - Test Your Circle Drawing Skills',
    description: 'Challenge yourself to draw a perfect circle! Test your precision and get scored on how close you can get to drawing the perfect circle.',
  },
  robots: {
    index: true,
    follow: true,
  },
}

export default async function LocaleLayout({
  children,
  params
}: {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}) {
  const { locale } = await params

  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as Locale)) {
    notFound()
  }

  const baseUrl = 'https://drawaperfectcircle.com'

  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: 'Draw a Perfect Circle',
    description: 'Challenge yourself to draw a perfect circle! Test your precision and get scored on how close you can get to drawing the perfect circle.',
    url: baseUrl,
    applicationCategory: 'Game',
    operatingSystem: 'Any',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD'
    },
    inLanguage: locales,
    availableLanguage: locales.map(locale => ({
      '@type': 'Language',
      name: locale,
      alternateName: locale
    }))
  }

  return (
    <html lang={locale}>
      <head>
        {/* Hreflang tags for SEO */}
        <link rel="alternate" hrefLang="x-default" href={baseUrl} />
        {locales.map(loc => (
          <link
            key={loc}
            rel="alternate"
            hrefLang={loc}
            href={`${baseUrl}/${loc}`}
          />
        ))}

        {/* Structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      </head>
      <body className={inter.className}>
        <GoogleAnalytics />
        <NextIntlClientProvider initialLocale={locale as Locale}>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
